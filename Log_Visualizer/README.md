# Magentic-One 工作流可视化工具

一个基于 Streamlit 的交互式工具，用于可视化和分析 Magentic-One 多 Agent 系统的工作流程。

## 🌟 功能特性

- **三文件支持**：同时解析 *UTC.json、*_log_formatted.json 和 *_llm_calls_formatted.json
- **线性时间线**：简化的线性工作流可视化，替代复杂网络图
- **完整日志查看**：支持查看 payload、消息等非 LLM 调用记录
- **智能关联分析**：基于时间戳精确匹配工作流步骤与日志记录
- **交互式详情**：点击步骤查看完整信息，包括 LLM 调用和完整日志

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动应用

```bash
./run.sh
```

### 3. 访问应用

浏览器会自动打开，或手动访问：

```text
http://localhost:8504
```

### 4. 使用步骤

1. **选择文件组**：在左侧边栏选择要分析的日志文件组
2. **加载数据**：点击"🔄 加载文件"按钮
3. **查看时间线**：在主界面查看线性工作流时间线
4. **查看详情**：选择步骤查看详细信息，包括 LLM 调用和完整日志

## 📁 支持的文件格式

工具会自动搜索并匹配以下三种文件：

- `*UTC.json`: 工作流步骤记录
- `*_log_formatted.json`: 完整日志记录（包含 payload、消息等）
- `*_llm_calls_formatted.json`: LLM 调用详情

文件需要放在 `../logs/generated/` 目录下。

## 🎯 主要界面

### 侧边栏
- **文件选择**：自动发现的日志文件组
- **加载按钮**：加载选中的文件组进行分析

### 主界面
- **📊 统计概览**：总步骤数、耗时、Token 使用量、活跃 Agent 数
- **📈 工作流时间线**：线性时间线，显示 Agent 序列和活动
- **🔍 步骤详情**：选择步骤查看详细信息
  - 步骤内容和时间戳
  - 关联的 LLM 调用详情
  - 相关日志记录（按类型分组）
  - Payload 和消息内容

## 🔧 故障排除

### 找不到日志文件
确保日志文件位于 `../logs/generated/` 目录下，且包含三种类型的文件。

### 依赖安装失败
```bash
conda create -n magentic-viz python=3.9
conda activate magentic-viz
pip install -r requirements.txt
```

### 端口被占用
修改 `run.sh` 中的端口号，或直接运行：
```bash
streamlit run visualize_log.py --server.port 8505
```

## 📂 文件结构

```
debug/
├── visualize_log.py      # 主程序
├── requirements.txt      # 依赖列表
├── run.sh               # 启动脚本
└── README.md            # 说明文档
```

