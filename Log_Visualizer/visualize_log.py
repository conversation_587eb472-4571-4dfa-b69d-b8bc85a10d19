#!/usr/bin/env python3
"""
Magentic-One 工作流可视化工具
=========================

一个基于 Streamlit 的交互式工具，用于可视化和分析 Magentic-One 多 Agent 系统的工作流程。
支持同时解析 *UTC.json 和 *_log_formatted.json 文件，生成交互式图谱展示 Agent 协作流程。

功能特性：
- 交互式工作流图谱：使用 Pyvis 生成可点击的节点图谱
- 双文件关联分析：同时解析核心流程和 LLM 调用详情
- 节点详情展示：点击节点查看完整的输入输出信息
- 时间线可视化：展示 Agent 交互的时间序列
- 统计分析：Token 使用量、Agent 活跃度等指标

技术栈：
- streamlit: Web 应用框架
- pyvis: 交互式网络图谱
- pandas: 数据处理
- plotly: 图表可视化

作者: Augment Agent
日期: 2025-08-02
"""

import streamlit as st
import json
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timezone
import os
import re
from pathlib import Path
import tempfile
from pyvis.network import Network
import streamlit.components.v1 as components

# 配置 Streamlit 页面
st.set_page_config(
    page_title="Magentic-One 工作流可视化",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

def extract_agent_name(agent_id):
    """从 agent_id 字符串中提取简洁的 Agent 名称"""
    if not agent_id:
        return "Unknown"
    
    # 提取主要的 Agent 名称
    if "/" in agent_id:
        agent_name = agent_id.split("/")[0]
    else:
        agent_name = agent_id
    
    # 清理常见模式
    if "_" in agent_name:
        parts = agent_name.split("_")
        if len(parts) > 1:
            agent_name = parts[0]
    
    # 移除常见前缀和后缀
    agent_name = agent_name.replace("MagenticOne", "")
    
    return agent_name if agent_name else "Unknown"

def parse_timestamp(timestamp_str):
    """解析时间戳字符串，支持多种格式，统一返回 timezone-naive datetime"""
    # 检查输入是否有效
    if not timestamp_str or not isinstance(timestamp_str, str):
        return datetime.now()

    try:
        if "," in timestamp_str:
            # 格式: "2025-07-27 03:26:42,413"
            dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S,%f")
        elif "T" in timestamp_str:
            # ISO 格式
            if timestamp_str.endswith("Z"):
                timestamp_str = timestamp_str[:-1] + "+00:00"
            dt = datetime.fromisoformat(timestamp_str)
            # 转换为 timezone-naive
            if dt.tzinfo is not None:
                dt = dt.replace(tzinfo=None)
        else:
            # 标准格式
            dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

        return dt
    except Exception as e:
        # 静默处理解析错误，避免在界面显示过多警告
        return datetime.now()

def find_log_files():
    """自动发现当前目录下的日志文件"""
    current_dir = Path(".")
    logs_dir = Path("../logs/generated")

    # 搜索路径
    search_paths = [current_dir, logs_dir]

    utc_files = []
    log_formatted_files = []
    llm_calls_files = []

    for search_path in search_paths:
        if search_path.exists():
            # 查找 *UTC.json 文件
            utc_pattern = search_path.glob("*UTC.json")
            utc_files.extend(utc_pattern)

            # 查找 *_log_formatted.json 文件（完整日志）
            log_formatted_pattern = search_path.glob("*_log_formatted.json")
            log_formatted_files.extend(log_formatted_pattern)

            # 查找 *_llm_calls_formatted.json 文件（LLM 调用）
            llm_calls_pattern = search_path.glob("*_llm_calls_formatted.json")
            llm_calls_files.extend(llm_calls_pattern)

    return (sorted(utc_files, key=lambda x: x.stat().st_mtime, reverse=True),
            sorted(log_formatted_files, key=lambda x: x.stat().st_mtime, reverse=True),
            sorted(llm_calls_files, key=lambda x: x.stat().st_mtime, reverse=True))

def load_utc_file(file_path):
    """加载 *UTC.json 文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        st.error(f"加载 UTC 文件错误 {file_path}: {e}")
        return None

def load_llm_calls_file(file_path):
    """加载 *_llm_calls_formatted.json 文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        st.error(f"加载 LLM 调用文件错误 {file_path}: {e}")
        return None

def load_log_formatted_file(file_path):
    """加载 *_log_formatted.json 文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        st.error(f"加载完整日志文件错误 {file_path}: {e}")
        return None

def parse_log_formatted_data(log_data):
    """解析 *_log_formatted.json 数据，提取所有类型的日志记录"""
    if not log_data:
        return []

    parsed_logs = []
    for entry in log_data:
        timestamp = entry.get('timestamp', '') or ''
        content = entry.get('content', {})

        # 处理不同的 content 结构
        if isinstance(content, str):
            # content 是字符串，可能是纯文本或 JSON
            try:
                parsed_content = json.loads(content)
                if isinstance(parsed_content, dict):
                    content = parsed_content
                else:
                    content = {'raw': content}
            except json.JSONDecodeError:
                content = {'raw': content}
        elif isinstance(content, dict):
            # content 已经是字典，直接使用
            pass
        else:
            # 其他类型，转换为字符串
            content = {'raw': str(content)}

        # 提取基本信息，优先从 content 字典中获取
        log_entry = {
            'timestamp': timestamp,
            'type': content.get('type', 'Unknown'),
            'sender': content.get('sender', ''),
            'receiver': content.get('receiver', ''),
            'kind': content.get('kind', ''),
            'delivery_stage': content.get('delivery_stage', ''),
            'payload': content.get('payload', ''),
            'raw_content': content
        }

        # 如果基本字段为空，尝试从 raw 字段获取信息
        if log_entry['type'] == 'Unknown' and 'raw' in content:
            raw_text = content['raw']
            if 'LLMCall' in raw_text:
                log_entry['type'] = 'LLMCall'
            elif 'Message' in raw_text:
                log_entry['type'] = 'Message'
            elif 'Publishing message' in raw_text:
                log_entry['type'] = 'Message'

        # 尝试解析 payload 中的 JSON
        if log_entry['payload'] and log_entry['payload'] != '{}':
            try:
                if isinstance(log_entry['payload'], str):
                    log_entry['parsed_payload'] = json.loads(log_entry['payload'])
                else:
                    log_entry['parsed_payload'] = log_entry['payload']
            except:
                log_entry['parsed_payload'] = None
        else:
            log_entry['parsed_payload'] = None

        parsed_logs.append(log_entry)

    return parsed_logs

def match_files_by_timestamp(utc_files, log_formatted_files, llm_calls_files):
    """根据时间戳匹配 UTC 文件、完整日志文件和 LLM 调用文件"""
    matched_triplets = []

    for utc_file in utc_files:
        # 从文件名中提取时间戳
        utc_name = utc_file.stem
        timestamp_match = re.search(r'(\d{8}_\d{6}UTC)', utc_name)

        if timestamp_match:
            timestamp = timestamp_match.group(1)

            # 查找对应的完整日志文件
            log_formatted_file = None
            for log_file in log_formatted_files:
                if timestamp in log_file.name:
                    log_formatted_file = log_file
                    break

            # 查找对应的 LLM 调用文件
            llm_calls_file = None
            for llm_file in llm_calls_files:
                if timestamp in llm_file.name:
                    llm_calls_file = llm_file
                    break

            matched_triplets.append((utc_file, log_formatted_file, llm_calls_file))

    return matched_triplets

def process_workflow_data(utc_data, log_formatted_data, llm_calls_data):
    """处理和关联工作流数据，包括完整日志信息"""
    if not utc_data:
        return []

    workflow_steps = []

    # 解析完整日志数据
    parsed_logs = parse_log_formatted_data(log_formatted_data) if log_formatted_data else []

    # 处理 LLM 调用数据
    llm_calls_map = {}
    if llm_calls_data and 'llm_calls' in llm_calls_data:
        for call in llm_calls_data['llm_calls']:
            call_index = call.get('call_index')
            if call_index:
                llm_calls_map[call_index] = call

    # 处理工作流步骤
    if 'history' in utc_data:
        for step in utc_data['history']:
            step_data = {
                'step': step.get('step', 0),
                'role': step.get('role', 'Unknown'),
                'content': step.get('content', ''),
                'timestamp': parse_timestamp(step.get('timestamp', '') or ''),
                'llm_calls': [],
                'related_logs': []
            }

            # 尝试关联 LLM 调用（基于时间戳匹配）
            step_timestamp = step_data['timestamp']
            if step_timestamp:
                for call_index, call_data in llm_calls_map.items():
                    call_timestamp = parse_timestamp(call_data.get('timestamp', '') or '')
                    # 如果时间戳相近（5秒内），认为是关联的
                    if call_timestamp and abs((step_timestamp - call_timestamp).total_seconds()) < 5:
                        step_data['llm_calls'].append(call_data)

                # 查找相关的完整日志记录（时间窗口内的记录）
                for log_entry in parsed_logs:
                    log_timestamp = log_entry.get('timestamp', '') or ''
                    log_time = parse_timestamp(log_timestamp)

                    if log_time and abs((log_time - step_timestamp).total_seconds()) <= 60:  # 60秒窗口
                        step_data['related_logs'].append(log_entry)

            workflow_steps.append(step_data)

    return workflow_steps

def create_workflow_graph(workflow_steps):
    """创建交互式工作流图谱"""
    net = Network(height="600px", width="100%", bgcolor="#222222", font_color="white")
    
    # 配置物理引擎
    net.set_options("""
    var options = {
      "physics": {
        "enabled": true,
        "stabilization": {"iterations": 100}
      }
    }
    """)
    
    # Agent 颜色映射
    agent_colors = {
        'human': '#FF6B6B',
        'Orchestrator': '#4ECDC4', 
        'WebSurfer': '#45B7D1',
        'Coder': '#96CEB4',
        'ComputerTerminal': '#FFEAA7',
        'FileSurfer': '#DDA0DD'
    }
    
    # 添加节点
    for i, step in enumerate(workflow_steps):
        agent_name = extract_agent_name(step['role'])
        color = agent_colors.get(agent_name, '#95A5A6')
        
        # 创建节点标题（悬停显示）
        title = f"步骤 {step['step']}: {agent_name}\n"
        title += f"时间: {step['timestamp'].strftime('%H:%M:%S')}\n"
        title += f"内容: {step['content'][:100]}..."
        
        if step['llm_calls']:
            title += f"\nLLM 调用: {len(step['llm_calls'])} 次"
        
        net.add_node(
            i, 
            label=f"{step['step']}\n{agent_name}",
            color=color,
            title=title,
            size=20 + len(step['llm_calls']) * 5  # 根据 LLM 调用数量调整大小
        )
        
        # 添加边（连接到下一步）
        if i > 0:
            net.add_edge(i-1, i, color="#666666")
    
    return net

def create_linear_workflow_timeline(workflow_steps):
    """创建线性工作流时间线可视化"""
    if not workflow_steps:
        return None

    # Agent 颜色映射
    agent_colors = {
        'human': '#FF6B6B',
        'Orchestrator': '#4ECDC4',
        'WebSurfer': '#45B7D1',
        'Coder': '#96CEB4',
        'ComputerTerminal': '#FFEAA7',
        'FileSurfer': '#DDA0DD'
    }

    # 准备数据
    timeline_data = []
    for i, step in enumerate(workflow_steps):
        agent_name = extract_agent_name(step['role'])
        color = agent_colors.get(agent_name, '#95A5A6')

        timeline_data.append({
            'step': step['step'],
            'agent': agent_name,
            'timestamp': step['timestamp'],
            'content_preview': step['content'][:50] + '...' if len(step['content']) > 50 else step['content'],
            'llm_calls_count': len(step['llm_calls']),
            'logs_count': len(step.get('related_logs', [])),
            'color': color
        })

    # 创建 Plotly 时间线图
    fig = go.Figure()

    # 添加时间线点
    fig.add_trace(go.Scatter(
        x=[i for i in range(len(timeline_data))],
        y=[1] * len(timeline_data),  # 所有点在同一水平线上
        mode='markers+lines',
        marker=dict(
            size=20,
            color=[item['color'] for item in timeline_data],
            line=dict(width=2, color='white')
        ),
        line=dict(width=3, color='#CCCCCC'),
        text=[f"Step {item['step']}: {item['agent']}<br>{item['content_preview']}<br>LLM调用: {item['llm_calls_count']}, 日志: {item['logs_count']}"
              for item in timeline_data],
        hovertemplate='<b>%{text}</b><extra></extra>',
        name='工作流步骤'
    ))

    # 更新布局
    fig.update_layout(
        title='Magentic-One 工作流时间线',
        xaxis_title='步骤序号',
        yaxis=dict(visible=False),
        height=200,
        showlegend=False,
        hovermode='closest',
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)'
    )

    return fig

def display_step_details(step_data):
    """显示步骤的详细信息"""
    st.subheader(f"步骤 {step_data['step']} - {extract_agent_name(step_data['role'])}")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.write("**步骤内容:**")
        st.text_area("", value=step_data['content'], height=150, disabled=True)

        st.write("**时间戳:**", step_data['timestamp'])

    with col2:
        st.write("**统计信息:**")
        st.metric("LLM 调用次数", len(step_data['llm_calls']))
        st.metric("相关日志条数", len(step_data.get('related_logs', [])))

    # 显示 LLM 调用详情
    if step_data['llm_calls']:
        st.write("### 🤖 LLM 调用详情")
        for i, llm_call in enumerate(step_data['llm_calls']):
            with st.expander(f"LLM 调用 {i+1}"):
                st.json(llm_call)

    # 显示相关日志
    if step_data.get('related_logs'):
        st.write("### 📋 相关日志记录")

        # 按类型分组显示日志
        log_types = {}
        for log in step_data['related_logs']:
            log_type = log.get('type', 'Unknown')
            if log_type not in log_types:
                log_types[log_type] = []
            log_types[log_type].append(log)

        for log_type, logs in log_types.items():
            with st.expander(f"{log_type} ({len(logs)} 条记录)"):
                for i, log in enumerate(logs):
                    st.write(f"**记录 {i+1}:**")
                    st.write(f"**时间:** {log['timestamp'] if log['timestamp'] else '无时间戳'}")

                    # 显示基本信息（如果有的话）
                    if log['sender']:
                        st.write(f"**发送者:** {log['sender']}")
                    if log['receiver']:
                        st.write(f"**接收者:** {log['receiver']}")
                    if log['kind']:
                        st.write(f"**类型:** {log['kind']}")
                    if log['delivery_stage']:
                        st.write(f"**阶段:** {log['delivery_stage']}")

                    # 显示 payload（如果有的话）
                    if log['payload'] and log['payload'] != '{}':
                        st.write("**Payload:**")
                        if log.get('parsed_payload'):
                            st.json(log['parsed_payload'])
                        else:
                            st.code(log['payload'])

                    # 如果没有结构化信息，显示原始内容
                    elif 'raw' in log['raw_content']:
                        st.write("**内容:**")
                        raw_content = log['raw_content']['raw']
                        if len(raw_content) > 500:
                            st.text_area("", value=raw_content, height=150, disabled=True)
                        else:
                            st.code(raw_content)

                    # 显示完整的原始数据（可选）
                    with st.expander("查看原始数据"):
                        st.json(log['raw_content'])

                    st.divider()

if __name__ == "__main__":
    st.title("🤖 Magentic-One 工作流可视化")
    st.markdown("分析和可视化 Magentic-One 多 Agent 系统的协作工作流程")
    
    # 侧边栏配置
    st.sidebar.header("📁 文件选择")
    
    # 自动发现日志文件
    utc_files, log_formatted_files, llm_calls_files = find_log_files()
    matched_triplets = match_files_by_timestamp(utc_files, log_formatted_files, llm_calls_files)

    if matched_triplets:
        # 显示匹配的文件组
        file_options = []
        for utc_file, log_formatted_file, llm_calls_file in matched_triplets:
            option_name = f"{utc_file.stem}"
            file_options.append((option_name, utc_file, log_formatted_file, llm_calls_file))

        selected_option = st.sidebar.selectbox(
            "选择日志文件组:",
            options=range(len(file_options)),
            format_func=lambda x: file_options[x][0]
        )

        if st.sidebar.button("🔄 加载文件"):
            option_name, utc_file, log_formatted_file, llm_calls_file = file_options[selected_option]

            # 加载数据
            with st.spinner("正在加载和处理数据..."):
                utc_data = load_utc_file(utc_file)
                log_formatted_data = load_log_formatted_file(log_formatted_file) if log_formatted_file else None
                llm_calls_data = load_llm_calls_file(llm_calls_file) if llm_calls_file else None

                if utc_data:
                    # 处理工作流数据
                    workflow_steps = process_workflow_data(utc_data, log_formatted_data, llm_calls_data)

                    # 存储到 session state
                    st.session_state['workflow_steps'] = workflow_steps
                    st.session_state['utc_data'] = utc_data
                    st.session_state['log_formatted_data'] = log_formatted_data
                    st.session_state['llm_calls_data'] = llm_calls_data
                    st.session_state['selected_file'] = option_name

                    st.success(f"✅ 成功加载 {option_name}")
                else:
                    st.error("❌ UTC 文件加载失败")
    else:
        st.sidebar.warning("⚠️ 未找到匹配的日志文件对")
        st.sidebar.info("请确保在 ../logs/generated/ 目录下有对应的 *UTC.json 和 *_llm_calls_formatted.json 文件")
    
    # 主内容区域
    if 'workflow_steps' in st.session_state:
        workflow_steps = st.session_state['workflow_steps']
        utc_data = st.session_state['utc_data']
        llm_calls_data = st.session_state['llm_calls_data']
        
        st.header(f"📊 工作流分析: {st.session_state['selected_file']}")
        
        # 基本统计信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总步骤数", len(workflow_steps))
        
        with col2:
            total_duration = (workflow_steps[-1]['timestamp'] - workflow_steps[0]['timestamp']).total_seconds()
            st.metric("总耗时", f"{total_duration:.1f}s")
        
        with col3:
            if llm_calls_data and 'metadata' in llm_calls_data:
                total_tokens = llm_calls_data['metadata'].get('total_tokens', 0)
                st.metric("总 Token 数", f"{total_tokens:,}")
            else:
                st.metric("总 Token 数", "N/A")
        
        with col4:
            unique_agents = len(set(extract_agent_name(step['role']) for step in workflow_steps))
            st.metric("活跃 Agent 数", unique_agents)
        
        # 创建和显示线性工作流时间线
        st.subheader("📈 工作流时间线")

        with st.spinner("正在生成工作流时间线..."):
            timeline_fig = create_linear_workflow_timeline(workflow_steps)

            if timeline_fig:
                st.plotly_chart(timeline_fig, use_container_width=True)
            else:
                st.error("无法生成时间线图表")
    
        # 步骤详情展示
        st.subheader("🔍 步骤详情")

        # 选择步骤查看详情
        step_options = [f"步骤 {step['step']}: {extract_agent_name(step['role'])}" for step in workflow_steps]
        selected_step_idx = st.selectbox(
            "选择步骤查看详情:",
            options=range(len(workflow_steps)),
            format_func=lambda x: step_options[x]
        )

        if selected_step_idx is not None:
            # 使用新的详细显示函数
            display_step_details(workflow_steps[selected_step_idx])

        # 额外的统计分析
        st.subheader("📊 统计分析")

        # Agent 活动统计
        agent_stats = {}
        for step in workflow_steps:
            agent = extract_agent_name(step['role'])
            if agent not in agent_stats:
                agent_stats[agent] = {'steps': 0, 'llm_calls': 0, 'total_logs': 0}
            agent_stats[agent]['steps'] += 1
            agent_stats[agent]['llm_calls'] += len(step['llm_calls'])
            agent_stats[agent]['total_logs'] += len(step.get('related_logs', []))

        # 显示统计表格
        stats_df = pd.DataFrame.from_dict(agent_stats, orient='index')
        stats_df.index.name = 'Agent'
        stats_df.columns = ['步骤数', 'LLM调用数', '日志记录数']
        st.dataframe(stats_df)

    else:
        st.info("👆 请在侧边栏选择并加载日志文件以开始分析")
