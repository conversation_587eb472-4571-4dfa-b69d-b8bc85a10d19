#!/bin/bash

# Magentic-One 工作流可视化工具启动脚本
# 支持三种日志文件类型的解析和线性时间线可视化

echo "🚀 启动 Magentic-One 工作流可视化工具"
echo "================================================"

# 检查依赖
echo "📦 检查依赖..."
python -c "import streamlit, plotly, pandas, pyvis" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少必要依赖，请运行: pip install -r requirements.txt"
    exit 1
fi

echo "✅ 依赖检查完成"

# 检查日志文件
echo "📁 检查日志文件..."
LOG_COUNT=$(find ../logs/generated -name "*UTC.json" 2>/dev/null | wc -l)
if [ $LOG_COUNT -eq 0 ]; then
    echo "⚠️  未找到日志文件，请确保 ../logs/generated/ 目录下有日志文件"
else
    echo "✅ 发现 $LOG_COUNT 个日志文件"
fi

# 启动应用
echo "🌐 启动 Web 应用..."
echo "   访问地址: http://localhost:8504"
echo "   按 Ctrl+C 停止应用"
echo ""

streamlit run visualize_log.py --server.port 8504 --server.headless true
