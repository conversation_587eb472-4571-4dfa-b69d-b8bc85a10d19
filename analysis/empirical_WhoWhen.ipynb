{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f9f7181d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import pathlib\n", "import json\n", "from matplotlib import pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "14deb9b9", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_colwidth', 500)"]}, {"cell_type": "code", "execution_count": 3, "id": "c6237823", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current working directory: /mnt/AutoDebugging/analysis\n", "Project root directory: /mnt/AutoDebugging\n"]}], "source": ["# find out the current working directory\n", "import os\n", "\n", "current_dir = os.getcwd()\n", "project_root = pathlib.Path(current_dir).parent\n", "\n", "print(f\"Current working directory: {current_dir}\")\n", "print(f\"Project root directory: {project_root}\")"]}, {"cell_type": "code", "execution_count": 10, "id": "dbb353ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of JSON files processed: 58\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>history</th>\n", "      <th>question</th>\n", "      <th>ground_truth</th>\n", "      <th>is_corrected</th>\n", "      <th>question_ID</th>\n", "      <th>mistake_agent</th>\n", "      <th>mistake_step</th>\n", "      <th>mistake_reason</th>\n", "      <th>case_index</th>\n", "      <th>level</th>\n", "      <th>mistake_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[{'content': 'Where can I take martial arts classes within a five-minute walk from the New York Stock Exchange after work (7-9 pm)?\n", "', 'role': 'human'}, {'content': 'Initial plan:\n", "\n", "We are working to address the following user request:\n", "\n", "Where can I take martial arts classes within a five-minute walk from the New York Stock Exchange after work (7-9 pm)?\n", "\n", "\n", "To answer this request we have assembled the following team:\n", "\n", "Assistant: A helpful and general-purpose AI assistant that has strong language...</td>\n", "      <td>Where can I take martial arts classes within a five-minute walk from the New York Stock Exchange after work (7-9 pm)?</td>\n", "      <td><PERSON><PERSON>-Jitsu Wall Street</td>\n", "      <td>False</td>\n", "      <td>6e3be83d1949fa52cba03fb1ce4b5b3bf7e37a83fd7d67694b10b2e439d90cf8</td>\n", "      <td>WebSurfer</td>\n", "      <td>12</td>\n", "      <td>WebSurfer clicks on an irrelevant website and disrupts the task-solving process.</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               history  \\\n", "0  [{'content': 'Where can I take martial arts classes within a five-minute walk from the New York Stock Exchange after work (7-9 pm)?\n", "', 'role': 'human'}, {'content': 'Initial plan:\n", "\n", "We are working to address the following user request:\n", "\n", "Where can I take martial arts classes within a five-minute walk from the New York Stock Exchange after work (7-9 pm)?\n", "\n", "\n", "To answer this request we have assembled the following team:\n", "\n", "Assistant: A helpful and general-purpose AI assistant that has strong language...   \n", "\n", "                                                                                                                question  \\\n", "0  Where can I take martial arts classes within a five-minute walk from the New York Stock Exchange after work (7-9 pm)?   \n", "\n", "                         ground_truth  is_corrected  \\\n", "0  Renzo Gracie Jiu-Jitsu Wall Street         False   \n", "\n", "                                                        question_ID  \\\n", "0  6e3be83d1949fa52cba03fb1ce4b5b3bf7e37a83fd7d67694b10b2e439d90cf8   \n", "\n", "  mistake_agent mistake_step  \\\n", "0     WebSurfer           12   \n", "\n", "                                                                     mistake_reason  \\\n", "0  WebSurfer clicks on an irrelevant website and disrupts the task-solving process.   \n", "\n", "   case_index  level mistake_type  \n", "0           1    NaN          NaN  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["fp = os.path.join(project_root, r\"Agents_Failure_Attribution/Who&When/Hand-Crafted\")\n", "\n", "l_tmp = []\n", "for f in os.listdir(fp):\n", "    if f.endswith('.json'):\n", "        with open(os.path.join(fp, f), 'r') as file:\n", "            data = json.load(file)\n", "            data['case_index'] = int(f.split('.')[0])\n", "            l_tmp.append(data)\n", "\n", "print(f\"Number of JSON files processed: {len(l_tmp)}\")\n", "\n", "df_wwh = pd.DataFrame(l_tmp)\n", "df_wwh = df_wwh.sort_values(by='case_index').reset_index(drop=True)\n", "df_wwh.head(1)"]}, {"cell_type": "code", "execution_count": 11, "id": "b0ebb09e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>history</th>\n", "      <th>question</th>\n", "      <th>ground_truth</th>\n", "      <th>is_corrected</th>\n", "      <th>question_ID</th>\n", "      <th>mistake_agent</th>\n", "      <th>mistake_step</th>\n", "      <th>mistake_reason</th>\n", "      <th>case_index</th>\n", "      <th>level</th>\n", "      <th>mistake_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>[{'content': 'What percentage of the total penguin population according to the upper estimates on english Wikipedia at the end of 2012 is made up by the penguins in this file that don't live on Dream Island or have beaks longer than 42mm? Round to the nearest five decimal places.\n", "\n", "The question is about a file, document or image, which can be accessed by the filename '8d46b8d6-b38a-47ff-ac74-cda14cf2d19b.csv' in the current working directory.\n", "\n", "Here are the file's contents:\n", "\n", "species,island,bil...</td>\n", "      <td>What percentage of the total penguin population according to the upper estimates on english Wikipedia at the end of 2012 is made up by the penguins in this file that don't live on Dream Island or have beaks longer than 42mm? Round to the nearest five decimal places.</td>\n", "      <td>0.00033</td>\n", "      <td>False</td>\n", "      <td>8d46b8d6-b38a-47ff-ac74-cda14cf2d19b</td>\n", "      <td>Orchestrator</td>\n", "      <td>14</td>\n", "      <td>The code is wrong.</td>\n", "      <td>14</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                history  \\\n", "13  [{'content': 'What percentage of the total penguin population according to the upper estimates on english Wikipedia at the end of 2012 is made up by the penguins in this file that don't live on Dream Island or have beaks longer than 42mm? Round to the nearest five decimal places.\n", "\n", "The question is about a file, document or image, which can be accessed by the filename '8d46b8d6-b38a-47ff-ac74-cda14cf2d19b.csv' in the current working directory.\n", "\n", "Here are the file's contents:\n", "\n", "species,island,bil...   \n", "\n", "                                                                                                                                                                                                                                                                      question  \\\n", "13  What percentage of the total penguin population according to the upper estimates on english Wikipedia at the end of 2012 is made up by the penguins in this file that don't live on Dream Island or have beaks longer than 42mm? Round to the nearest five decimal places.   \n", "\n", "   ground_truth  is_corrected                           question_ID  \\\n", "13      0.00033         False  8d46b8d6-b38a-47ff-ac74-cda14cf2d19b   \n", "\n", "   mistake_agent mistake_step      mistake_reason  case_index  level  \\\n", "13  Orchestrator           14  The code is wrong.          14    3.0   \n", "\n", "   mistake_type  \n", "13          NaN  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df_wwh[df_wwh['case_index']==14]"]}, {"cell_type": "code", "execution_count": 5, "id": "976b1b26", "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "df_wwh['is_gaia'] = df_wwh['level'].apply(lambda x: 0 if pd.isna(x) else 1)\n", "df_wwh['step_count'] = df_wwh['history'].apply(lambda x: len(x) if isinstance(x, list) else 0)\n", "df_wwh['unique_roles'] = df_wwh['history'].apply(\n", "    lambda x: list(set([e['role'].split(\" (\")[0].strip() for e in x])) if isinstance(x, list) else []\n", ")\n", "df_wwh['unique_role_count'] = df_wwh['unique_roles'].apply(lambda x: len(x))\n", "\n", "df_wwh['step_count_per_role'] = df_wwh['history'].apply(\n", "    lambda x: Counter([e['role'].split(\" (\")[0].strip() for e in x]) if isinstance(x, list) else Counter()\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "0f13c62c", "metadata": {}, "outputs": [{"data": {"image/png": "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***********************************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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# draw a stacked bar plot to show the distribution of step counts per role for each case\n", "def plot_step_counts_per_role(df, title='Step Counts per Role', color_by_level=False):\n", "    role_counts = df['step_count_per_role'].apply(lambda x: dict(x)).apply(pd.Series).fillna(0)\n", "    role_counts = role_counts.astype(int)\n", "    \n", "    # Set the index to case_index for plotting\n", "    role_counts.index = df['case_index']\n", "\n", "    ax = role_counts.plot(kind='bar', stacked=True, figsize=(10, 5))\n", "    ax.set_title(title)\n", "    ax.set_xlabel('Case Index')\n", "    ax.set_ylabel('Step Count')\n", "    plt.legend(title='Role', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    \n", "    # If color_by_level is True, set the xlabels color based on level\n", "    if color_by_level and 'level' in df.columns:\n", "        # Define colors for different levels\n", "        level_colors = {1.0: 'green', 2.0: 'orange', 3.0: 'red'}\n", "        \n", "        # Set the color of the xlabels based on level\n", "        for i, case_idx in enumerate(df['case_index']):\n", "            level = df.loc[df['case_index'] == case_idx, 'level'].values[0]\n", "            ax.get_xticklabels()[i].set_color(level_colors.get(level, 'black'))\n", "        \n", "        # Add a legend for level colors\n", "        legend_text = \", \".join([f\"Level {k}: {v}\" for k, v in level_colors.items()])\n", "        ax.set_xlabel(f'Case Index ({legend_text})')\n", "            \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Plot for Gaia cases with color-coded xlabels based on level\n", "plot_step_counts_per_role(df_wwh[df_wwh['is_gaia'] == 1], \n", "                         title='Step Counts per Role for Gaia Cases', \n", "                         color_by_level=True)\n", "\n", "# Plot for Non-Gaia cases\n", "plot_step_counts_per_role(df_wwh[df_wwh['is_gaia'] == 0], \n", "                         title='Step Counts per Role for Assistant<PERSON><PERSON><PERSON>')"]}, {"cell_type": "code", "execution_count": 9, "id": "b50d37d1", "metadata": {}, "outputs": [], "source": ["df_wwh[['case_index', 'is_gaia', 'level', 'question', 'mistake_reason', 'mistake_step', 'mistake_agent', 'step_count']].sort_values(by=['is_gaia', 'case_index']).to_csv(\n", "    os.path.join(project_root, 'analysis', 'wwh_analysis.csv'), index=False\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "343aa13c", "metadata": {}, "outputs": [], "source": ["df_wwh.to_csv(os.path.join(project_root, 'analysis', 'df_wwh.csv'), index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "847d98f8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ada", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}