{"cells": [{"cell_type": "code", "execution_count": null, "id": "e60913e1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Z  X  Y\n", "0  1  0  1\n", "1  1  0  1\n", "2  1  1  0\n", "3  1  1  0\n", "4  0  1  0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "# Simulate data\n", "np.random.seed(0)\n", "N = 10\n", "\n", "# Z = TaskDifficulty: 0 = easy, 1 = hard\n", "Z = np.random.binomial(1, 0.5, N)\n", "\n", "# X = QueryQuality: 1 = good query, 0 = bad query\n", "# Good queries more likely when task is easy (Z=0)\n", "X = np.random.binomial(1, 0.7 - 0.4 * Z, N)\n", "\n", "# Y = FailureFlag: 1 = failure, 0 = correct result\n", "# Failure more likely with bad query (X=0), and with hard tasks (Z=1)\n", "Y = np.random.binomial(1, 0.6 - 0.5 * X + 0.4 * Z)\n", "\n", "data = pd.DataFrame({\n", "    \"Z\": <PERSON>,  # <PERSON><PERSON><PERSON><PERSON><PERSON>y\n", "    \"X\": X,  # QueryQuality\n", "    \"Y\": Y   # FailureFlag\n", "})\n", "\n", "# Define causal graph\n", "causal_graph = \"\"\"\n", "digraph {\n", "    Z -> X;\n", "    Z -> Y;\n", "    X -> Y;\n", "}\n", "\"\"\"\n", "\n", "data.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "4aefb2f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Identified estimand: Estimand type: EstimandType.NONPARAMETRIC_ATE\n", "\n", "### Estimand : 1\n", "Estimand name: backdoor\n", "Estimand expression:\n", " d          \n", "────(E[Y|Z])\n", "d[X]        \n", "Estimand assumption 1, Unconfoundedness: If U→{X} and U→Y then P(Y|X,Z,U) = P(Y|X,Z)\n", "\n", "### Estimand : 2\n", "Estimand name: iv\n", "No such variable(s) found!\n", "\n", "### Estimand : 3\n", "Estimand name: frontdoor\n", "No such variable(s) found!\n", "\n", "### Estimand : 4\n", "Estimand name: general_adjustment\n", "Estimand expression:\n", " d          \n", "────(E[Y|Z])\n", "d[X]        \n", "Estimand assumption 1, Unconfoundedness: If U→{X} and U→Y then P(Y|X,Z,U) = P(Y|X,Z)\n", "\n", "Estimated ATE: -0.49\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Refutation result:\n", " Refute: Use a Placebo Treatment\n", "Estimated effect:-0.49\n", "New effect:0.028000000000000004\n", "p value:0.8999999999999999\n", "\n"]}], "source": ["from dowhy import CausalModel\n", "\n", "# Create causal model\n", "model = CausalModel(data=data, treatment=\"X\", outcome=\"Y\", graph=causal_graph)\n", "\n", "# Identify causal effect\n", "identified_estimand = model.identify_effect()\n", "print(\"Identified estimand:\", identified_estimand)\n", "\n", "# Estimate effect\n", "estimate = model.estimate_effect(identified_estimand, method_name=\"backdoor.propensity_score_matching\")\n", "print(\"Estimated ATE:\", estimate.value)\n", "\n", "# Refute with placebo\n", "refute = model.refute_estimate(identified_estimand, estimate, method_name=\"placebo_treatment_refuter\")\n", "print(\"Refutation result:\\n\", refute)\n"]}, {"cell_type": "code", "execution_count": null, "id": "6074c33e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ada", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}