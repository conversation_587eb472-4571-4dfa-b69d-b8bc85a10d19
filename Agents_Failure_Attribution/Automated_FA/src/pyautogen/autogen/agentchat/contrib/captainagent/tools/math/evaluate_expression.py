# Copyright (c) 2023 - 2024, Owners of https://github.com/ag2ai
#
# SPDX-License-Identifier: Apache-2.0
def evaluate_expression(expression):
    """
    Evaluates a mathematical expression with support for floor function notation and power notation.

    Args:
        expression (str): The mathematical expression to evaluate. It can only contain one symbol 'x'.

    Returns:
        Union[sympy.Expr, str]: The evaluated result as a sympy expression if successful,
        otherwise an error message as a string.

    """
    from sympy import symbols, sympify

    # Replace power with ** for sympy
    expression = expression.replace("^", "**")
    # Replace the floor function notation
    expression = expression.replace("\\lfloor", "floor(").replace("\\rfloor", ")")
    try:
        # Create a symbol 'x' for use in case it is in the expression
        symbols("x")
        # Evaluate the expression
        result = sympify(expression)
        return result
    except Exception as e:
        return str(e)
