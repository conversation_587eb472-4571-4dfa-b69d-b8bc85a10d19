# Copyright (c) 2023 - 2024, Owners of https://github.com/ag2ai
#
# SPDX-License-Identifier: Apache-2.0
def modular_inverse_sum(expressions, modulus):
    """
    Calculates the sum of modular inverses of the given expressions modulo the specified modulus.

    Args:
        expressions (list): A list of numbers for which the modular inverses need to be calculated.
        modulus (int): The modulus value.

    Returns:
        int: The sum of modular inverses modulo the specified modulus.
    """
    from sympy import mod_inverse

    mod_sum = 0
    for number in expressions:
        try:
            mod_sum += mod_inverse(number, modulus)
        except ValueError:
            pass  # If modular inverse does not exist, skip the term
    return mod_sum % modulus
