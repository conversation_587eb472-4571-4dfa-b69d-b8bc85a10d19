#!/bin/bash

echo "Running evaluations for with_gt data..."
for dir in outputs/with_gt/*/; do
    if [ -d "$dir" ]; then
        echo "Processing directory: $dir"
        for file in "$dir"*.txt; do
            if [ -f "$file" ] && [[ ! $(basename "$file") == evaluation_* ]]; then
                echo "Evaluating: $file"

                # Determine data_path based on filename
                if [[ $(basename "$file") == *"handcrafted"* ]]; then
                    data_path="../Who&When/Hand-Crafted"
                elif [[ $(basename "$file") == *"alg_generated"* ]]; then
                    data_path="../Who&When/Algorithm-Generated"
                else
                    echo "Warning: Cannot determine data path for $file, using default"
                    data_path="../Who&When/Algorithm-Generated"
                fi

                python3 evaluate_v2.py --eval_file "$file" --data_path "$data_path"
            fi
        done
    fi
done

# echo "Running evaluations for without_gt data..."
# for dir in outputs/without_gt/*/; do
#     if [ -d "$dir" ]; then
#         echo "Processing directory: $dir"
#         for file in "$dir"*.txt; do
#             if [ -f "$file" ] && [[ ! $(basename "$file") == evaluation_* ]]; then
#                 echo "Evaluating: $file"

#                 # Determine data_path based on filename
#                 if [[ $(basename "$file") == *"handcrafted"* ]]; then
#                     data_path="../Who&When/Hand-Crafted"
#                 elif [[ $(basename "$file") == *"alg_generated"* ]]; then
#                     data_path="../Who&When/Algorithm-Generated"
#                 else
#                     echo "Warning: Cannot determine data path for $file, using default"
#                     data_path="../Who&When/Algorithm-Generated"
#                 fi

#                 python3 evaluate_v2.py --eval_file "$file" --data_path "$data_path"
#             fi
#         done
#     fi
# done

echo "All evaluations completed!"
