{"name": "agdebugger-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@column-resizer/react": "^1.3.0", "@tippyjs/react": "^4.2.6", "axios": "^1.7.2", "d3": "^7.9.0", "flowbite-react": "^0.9.0", "flowbite-react-icons": "^1.0.7", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-force-graph": "^1.44.4"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/d3": "^7.4.3", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/react-modal": "^3.16.3", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "prettier": "^3.3.2", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.2.0"}}